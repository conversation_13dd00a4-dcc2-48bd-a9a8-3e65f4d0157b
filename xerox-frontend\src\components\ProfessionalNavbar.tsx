import React, { useState, useEffect } from 'react';
import { Navbar, Nav, NavDropdown, Container, Badge, Button, Offcanvas } from 'react-bootstrap';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  User, 
  Settings, 
  LogOut, 
  Menu, 
  X, 
  Home,
  FileText,
  Upload,
  MessageCircle,
  BarChart3,
  Store,
  Search,
  Moon,
  Sun,
  Printer,
  Download,
  Clock,
  DollarSign
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import '../styles/ProfessionalNavbar.css';

interface NavbarProps {
  onThemeToggle?: () => void;
  isDarkMode?: boolean;
}

const ProfessionalNavbar: React.FC<NavbarProps> = ({ onThemeToggle, isDarkMode = false }) => {
  const { user, logout } = useAuth();
  const [showOffcanvas, setShowOffcanvas] = useState(false);
  const [notifications, setNotifications] = useState(3); // Mock notification count
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = () => {
    logout();
  };



  const getNavItems = () => {
    if (user?.userType === 'Student') {
      return [
        { icon: Home, label: 'Dashboard', href: '/student-dashboard', active: true },
        { icon: Upload, label: 'Upload File', href: '/upload' },
        { icon: FileText, label: 'My Jobs', href: '/my-jobs' },
        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 2 },
        { icon: Store, label: 'Xerox Centers', href: '/xerox-centers' }
      ];
    } else if (user?.userType === 'XeroxCenter') {
      return [
        { icon: BarChart3, label: 'Dashboard', href: '/xerox-dashboard', active: true },
        { icon: FileText, label: 'Job Queue', href: '/job-queue' },
        { icon: Printer, label: 'Active Jobs', href: '/active-jobs', badge: 5 },
        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 3 },
        { icon: DollarSign, label: 'Revenue', href: '/revenue' },
        { icon: Settings, label: 'Settings', href: '/settings' }
      ];
    }
    return [];
  };

  const navItems = getNavItems();

  return (
    <>
      <motion.div
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
        className={`professional-navbar-wrapper ${isScrolled ? 'scrolled' : ''}`}
      >
        <Navbar 
          expand="lg" 
          className={`professional-navbar ${isDarkMode ? 'dark-mode' : ''}`}
          fixed="top"
        >
          <Container fluid>
            {/* Brand Logo */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Navbar.Brand href="/" className="brand-logo">
                <div className="logo-container">
                  <div className="logo-icon">
                    <Printer size={28} />
                  </div>
                  <div className="logo-text">
                    <span className="brand-name">XeroxHub</span>
                    <span className="brand-tagline">Print Solutions</span>
                  </div>
                </div>
              </Navbar.Brand>
            </motion.div>

            {/* Mobile Menu Toggle */}
            <div className="d-lg-none">
              <Button
                variant="outline-light"
                className="mobile-menu-btn"
                onClick={() => setShowOffcanvas(true)}
              >
                <Menu size={20} />
              </Button>
            </div>

            {/* Desktop Navigation */}
            <Navbar.Collapse id="navbar-nav" className="d-none d-lg-flex">
              <Nav className="nav-items me-auto">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.label}
                    whileHover={{ y: -2 }}
                    whileTap={{ y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Nav.Link 
                      href={item.href}
                      className={`nav-item ${item.active ? 'active' : ''}`}
                    >
                      <item.icon size={18} />
                      <span>{item.label}</span>
                      {item.badge && (
                        <Badge bg="danger" className="nav-badge">
                          {item.badge}
                        </Badge>
                      )}
                    </Nav.Link>
                  </motion.div>
                ))}
              </Nav>

              {/* Right Side Actions */}
              <Nav className="nav-actions">
                {/* Search */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="search-container"
                >
                  <Button variant="outline-light" className="action-btn search-btn">
                    <Search size={18} />
                  </Button>
                </motion.div>

                {/* Theme Toggle */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="outline-light"
                    className="action-btn theme-btn"
                    onClick={onThemeToggle}
                  >
                    {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}
                  </Button>
                </motion.div>

                {/* Notifications */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="notification-container"
                >
                  <Button variant="outline-light" className="action-btn notification-btn">
                    <Bell size={18} />
                    {notifications > 0 && (
                      <Badge bg="danger" className="notification-badge">
                        {notifications}
                      </Badge>
                    )}
                  </Button>
                </motion.div>

                {/* User Profile Dropdown */}
                <NavDropdown
                  title={
                    <div className="user-profile">
                      <div className="user-avatar">
                        <User size={18} />
                      </div>
                      <div className="user-info d-none d-xl-block">
                        <span className="user-name">{user?.username}</span>
                        <span className="user-role">{user?.userType}</span>
                      </div>
                    </div>
                  }
                  id="user-dropdown"
                  align="end"
                  className="user-dropdown"
                >
                  <NavDropdown.Item href="/profile">
                    <User size={16} />
                    Profile
                  </NavDropdown.Item>
                  <NavDropdown.Item href="/settings">
                    <Settings size={16} />
                    Settings
                  </NavDropdown.Item>
                  <NavDropdown.Divider />
                  <NavDropdown.Item onClick={handleLogout} className="logout-item">
                    <LogOut size={16} />
                    Logout
                  </NavDropdown.Item>
                </NavDropdown>
              </Nav>
            </Navbar.Collapse>
          </Container>
        </Navbar>
      </motion.div>

      {/* Mobile Offcanvas Menu */}
      <Offcanvas
        show={showOffcanvas}
        onHide={() => setShowOffcanvas(false)}
        placement="end"
        className={`mobile-offcanvas ${isDarkMode ? 'dark-mode' : ''}`}
      >
        <Offcanvas.Header>
          <Offcanvas.Title>
            <div className="mobile-brand">
              <Printer size={24} />
              <span>XeroxHub</span>
            </div>
          </Offcanvas.Title>
          <Button
            variant="outline-secondary"
            onClick={() => setShowOffcanvas(false)}
            className="close-btn"
          >
            <X size={20} />
          </Button>
        </Offcanvas.Header>
        <Offcanvas.Body>
          <div className="mobile-nav-items">
            {navItems.map((item, index) => (
              <motion.a
                key={item.label}
                href={item.href}
                className={`mobile-nav-item ${item.active ? 'active' : ''}`}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => setShowOffcanvas(false)}
              >
                <item.icon size={20} />
                <span>{item.label}</span>
                {item.badge && (
                  <Badge bg="danger" className="mobile-nav-badge">
                    {item.badge}
                  </Badge>
                )}
              </motion.a>
            ))}
          </div>

          <div className="mobile-actions">
            <Button
              variant="outline-primary"
              className="mobile-action-btn"
              onClick={onThemeToggle}
            >
              {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}
              {isDarkMode ? 'Light Mode' : 'Dark Mode'}
            </Button>
            
            <Button
              variant="outline-danger"
              className="mobile-action-btn"
              onClick={handleLogout}
            >
              <LogOut size={18} />
              Logout
            </Button>
          </div>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};

export default ProfessionalNavbar;
