/* Layout Styles */

.layout-wrapper {
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
  transition: all 0.3s ease;
}

.layout-wrapper.light-mode {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: var(--text-color);
}

.layout-wrapper.dark-mode {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: var(--text-color);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 1;
  transition: all 0.5s ease;
}

.page-content {
  position: relative;
  z-index: 2;
}

/* Background Decorations */
.background-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  animation: float 20s infinite ease-in-out;
}

.decoration-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: -5%;
  animation-delay: -7s;
}

.decoration-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: -14s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.3;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.5;
  }
}

.dark-mode .decoration {
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

/* Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.fab-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.fab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.fab-btn:hover::before {
  left: 100%;
}

.fab-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.fab-btn:active {
  transform: scale(0.95);
}

/* Theme Indicator */
.theme-indicator {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  z-index: 999;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: themeIndicatorPulse 2s ease-in-out;
}

.dark-mode .theme-indicator {
  background: rgba(45, 45, 45, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

@keyframes themeIndicatorPulse {
  0% { transform: translateY(-50%) scale(1); }
  50% { transform: translateY(-50%) scale(1.1); }
  100% { transform: translateY(-50%) scale(1); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Global Theme Variables */
:root {
  --transition-speed: 0.3s;
  --border-radius: 12px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dark-theme {
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  --box-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* Content Animations */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Glass Effect Utility */
.glass-effect {
  background: var(--glass-background);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
}

/* Card Hover Effects */
.hover-lift {
  transition: all var(--transition-speed) ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
}

/* Button Styles */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .floating-action-btn {
    bottom: 20px;
    right: 20px;
  }
  
  .fab-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .theme-indicator {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    right: 15px;
  }
  
  .decoration-1,
  .decoration-2,
  .decoration-3 {
    opacity: 0.5;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding-top: 80px !important;
  }
  
  .decoration {
    display: none;
  }
}

/* Print Styles */
@media print {
  .floating-action-btn,
  .theme-indicator,
  .background-decorations {
    display: none !important;
  }
  
  .main-content {
    padding-top: 0 !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .decoration {
    display: none;
  }
  
  .glass-effect {
    background: var(--surface-color);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .decoration {
    animation: none;
  }
}
