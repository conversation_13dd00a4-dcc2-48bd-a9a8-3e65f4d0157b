/* Professional Navbar Styles */

.professional-navbar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1050;
  transition: all 0.3s ease;
}

.professional-navbar-wrapper.scrolled {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.professional-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 0;
  transition: all 0.3s ease;
}

.professional-navbar.dark-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.scrolled .professional-navbar {
  padding: 0.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Brand Logo */
.brand-logo {
  text-decoration: none;
  color: white !important;
  transition: all 0.3s ease;
}

.brand-logo:hover {
  color: rgba(255, 255, 255, 0.9) !important;
  text-decoration: none;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.logo-icon:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(5deg);
}

.logo-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.5px;
  background: linear-gradient(45deg, #ffffff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Navigation Items */
.nav-items {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-item {
  display: flex !important;
  align-items: center;
  gap: 8px;
  padding: 0.75rem 1rem !important;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.9) !important;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  text-decoration: none;
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-badge {
  font-size: 0.7rem;
  padding: 0.25em 0.5em;
  margin-left: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Action Buttons */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-radius: 10px;
  padding: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-btn {
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Theme Toggle */
.theme-btn {
  position: relative;
}

.theme-btn svg {
  transition: all 0.3s ease;
}

.theme-btn:hover svg {
  transform: rotate(180deg);
}

/* Notifications */
.notification-container {
  position: relative;
}

.notification-btn {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.6rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* User Profile */
.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  padding: 0.5rem;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.8;
  text-transform: capitalize;
}

/* Dropdown Styles */
.user-dropdown .dropdown-toggle {
  background: transparent !important;
  border: none !important;
  color: white !important;
  padding: 0 !important;
}

.user-dropdown .dropdown-toggle:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 10px;
}

.user-dropdown .dropdown-toggle::after {
  display: none;
}

.user-dropdown .dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.user-dropdown .dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0.75rem;
  border-radius: 8px;
  color: #333;
  font-weight: 500;
  transition: all 0.2s ease;
}

.user-dropdown .dropdown-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateX(5px);
}

.logout-item:hover {
  background: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
}

/* Mobile Styles */
.mobile-menu-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-radius: 8px;
}

.mobile-offcanvas {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.mobile-offcanvas.dark-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.mobile-brand {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 700;
  font-size: 1.2rem;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.mobile-nav-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 1rem;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.mobile-nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  transform: translateX(10px);
}

.mobile-nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.mobile-nav-badge {
  margin-left: auto;
  font-size: 0.7rem;
}

.mobile-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: auto;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.mobile-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0.75rem;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.mobile-action-btn:hover {
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .brand-name {
    font-size: 1.3rem;
  }
  
  .brand-tagline {
    font-size: 0.7rem;
  }
  
  .logo-icon {
    padding: 6px;
  }
}

@media (max-width: 575.98px) {
  .logo-text {
    display: none;
  }
  
  .professional-navbar {
    padding: 0.5rem 0;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Glassmorphism Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hover Glow Effect */
.glow-on-hover {
  position: relative;
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

/* Loading Shimmer */
.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
