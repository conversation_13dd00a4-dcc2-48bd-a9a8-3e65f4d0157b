import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
  theme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState<boolean>(() => {
    // Check localStorage first
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    
    // Check system preference
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  const toggleTheme = () => {
    setIsDarkMode(prev => {
      const newTheme = !prev;
      localStorage.setItem('theme', newTheme ? 'dark' : 'light');
      return newTheme;
    });
  };

  useEffect(() => {
    // Apply theme to document
    const root = document.documentElement;
    
    if (isDarkMode) {
      root.classList.add('dark-theme');
      root.classList.remove('light-theme');
      document.body.setAttribute('data-bs-theme', 'dark');
    } else {
      root.classList.add('light-theme');
      root.classList.remove('dark-theme');
      document.body.setAttribute('data-bs-theme', 'light');
    }

    // Update CSS custom properties
    root.style.setProperty('--primary-color', isDarkMode ? '#667eea' : '#007bff');
    root.style.setProperty('--secondary-color', isDarkMode ? '#764ba2' : '#6c757d');
    root.style.setProperty('--background-color', isDarkMode ? '#1a1a1a' : '#ffffff');
    root.style.setProperty('--surface-color', isDarkMode ? '#2d2d2d' : '#f8f9fa');
    root.style.setProperty('--text-color', isDarkMode ? '#ffffff' : '#212529');
    root.style.setProperty('--text-muted', isDarkMode ? '#adb5bd' : '#6c757d');
    root.style.setProperty('--border-color', isDarkMode ? '#404040' : '#dee2e6');
    root.style.setProperty('--shadow-color', isDarkMode ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.1)');
    
    // Gradient colors
    root.style.setProperty('--gradient-primary', isDarkMode 
      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
      : 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)'
    );
    
    root.style.setProperty('--gradient-secondary', isDarkMode
      ? 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)'
      : 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
    );

    // Glass effect
    root.style.setProperty('--glass-background', isDarkMode
      ? 'rgba(45, 45, 45, 0.8)'
      : 'rgba(255, 255, 255, 0.8)'
    );

    root.style.setProperty('--glass-border', isDarkMode
      ? 'rgba(255, 255, 255, 0.1)'
      : 'rgba(0, 0, 0, 0.1)'
    );

  }, [isDarkMode]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      // Only update if user hasn't manually set a preference
      if (!localStorage.getItem('theme')) {
        setIsDarkMode(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const value: ThemeContextType = {
    isDarkMode,
    toggleTheme,
    theme: isDarkMode ? 'dark' : 'light'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
