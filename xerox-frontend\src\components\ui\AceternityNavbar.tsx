import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  User, 
  Settings, 
  LogOut, 
  Menu, 
  X, 
  Home,
  FileText,
  Upload,
  MessageCircle,
  BarChart3,
  Store,
  Search,
  Moon,
  Sun,
  Printer,
  DollarSign,
  ChevronDown
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { cn } from '../../lib/utils';

interface NavItem {
  icon: React.ComponentType<any>;
  label: string;
  href: string;
  active?: boolean;
  badge?: number;
}

const FloatingNav = () => {
  const { user, logout } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [notifications] = useState(3);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const getNavItems = (): NavItem[] => {
    if (user?.userType === 'Student') {
      return [
        { icon: Home, label: 'Dashboard', href: '/student-dashboard', active: true },
        { icon: Upload, label: 'Upload', href: '/upload' },
        { icon: FileText, label: 'My Jobs', href: '/my-jobs' },
        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 2 },
        { icon: Store, label: 'Centers', href: '/xerox-centers' }
      ];
    } else if (user?.userType === 'XeroxCenter') {
      return [
        { icon: BarChart3, label: 'Dashboard', href: '/xerox-dashboard', active: true },
        { icon: FileText, label: 'Jobs', href: '/job-queue' },
        { icon: Printer, label: 'Active', href: '/active-jobs', badge: 5 },
        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 3 },
        { icon: DollarSign, label: 'Revenue', href: '/revenue' }
      ];
    }
    return [];
  };

  const navItems = getNavItems();

  return (
    <>
      {/* Main Floating Navbar */}
      <motion.div
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "fixed top-4 left-1/2 transform -translate-x-1/2 z-50",
          "w-[95%] max-w-7xl mx-auto"
        )}
      >
        <div
          className={cn(
            "relative rounded-2xl border border-white/20 bg-white/10 backdrop-blur-md",
            "shadow-lg shadow-black/5 transition-all duration-300",
            isDarkMode ? "bg-black/20 border-white/10" : "bg-white/20 border-black/10",
            isScrolled && "shadow-xl shadow-black/10"
          )}
        >
          <div className="flex items-center justify-between px-6 py-4">
            {/* Logo */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="flex items-center space-x-3"
            >
              <div className="relative">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <Printer className="w-5 h-5 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  XeroxHub
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">Print Solutions</p>
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              {navItems.map((item, index) => (
                <motion.a
                  key={item.label}
                  href={item.href}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -2 }}
                  className={cn(
                    "relative flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200",
                    "hover:bg-white/20 dark:hover:bg-white/10",
                    item.active && "bg-white/20 dark:bg-white/10"
                  )}
                >
                  <item.icon className="w-4 h-4" />
                  <span className="text-sm font-medium">{item.label}</span>
                  {item.badge && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
                    >
                      {item.badge}
                    </motion.div>
                  )}
                </motion.a>
              ))}
            </div>

            {/* Right Actions */}
            <div className="flex items-center space-x-2">
              {/* Search */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="hidden sm:flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors"
              >
                <Search className="w-4 h-4" />
              </motion.button>

              {/* Theme Toggle */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={toggleTheme}
                className="flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors"
              >
                {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </motion.button>

              {/* Notifications */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors"
              >
                <Bell className="w-4 h-4" />
                {notifications > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
                  >
                    {notifications}
                  </motion.div>
                )}
              </motion.button>

              {/* User Menu */}
              <div className="relative group">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center space-x-2 px-3 py-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors"
                >
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <div className="hidden sm:block text-left">
                    <p className="text-sm font-medium">{user?.username}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{user?.userType}</p>
                  </div>
                  <ChevronDown className="w-4 h-4" />
                </motion.button>

                {/* Dropdown Menu */}
                <AnimatePresence>
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.95 }}
                    className="absolute right-0 top-full mt-2 w-48 rounded-xl bg-white/90 dark:bg-black/90 backdrop-blur-md border border-white/20 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <div className="p-2">
                      <a href="/profile" className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors">
                        <User className="w-4 h-4" />
                        <span className="text-sm">Profile</span>
                      </a>
                      <a href="/settings" className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors">
                        <Settings className="w-4 h-4" />
                        <span className="text-sm">Settings</span>
                      </a>
                      <hr className="my-2 border-white/20" />
                      <button
                        onClick={logout}
                        className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-red-500/20 text-red-500 transition-colors"
                      >
                        <LogOut className="w-4 h-4" />
                        <span className="text-sm">Logout</span>
                      </button>
                    </div>
                  </motion.div>
                </AnimatePresence>
              </div>

              {/* Mobile Menu Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors"
              >
                {isMobileMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-24 left-4 right-4 z-40 lg:hidden"
          >
            <div className="rounded-2xl bg-white/90 dark:bg-black/90 backdrop-blur-md border border-white/20 shadow-lg p-4">
              <div className="space-y-2">
                {navItems.map((item) => (
                  <a
                    key={item.label}
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={cn(
                      "flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors",
                      "hover:bg-white/20 dark:hover:bg-white/10",
                      item.active && "bg-white/20 dark:bg-white/10"
                    )}
                  >
                    <item.icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                    {item.badge && (
                      <div className="ml-auto w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                        {item.badge}
                      </div>
                    )}
                  </a>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default FloatingNav;
